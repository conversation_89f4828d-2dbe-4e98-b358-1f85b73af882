# TTS API with CosyVoice 2.0

基于CosyVoice 2.0的文本转语音API服务，支持长文本自动分段、语音合并和MinIO存储。

## 功能特性

- 🎵 **CosyVoice 2.0集成**: 使用最新的CosyVoice 2.0模型进行高质量TTS
- 📝 **智能文本分段**: 自动处理长文本，按标点符号智能分段
- 🔗 **音频合并**: 将多段音频无缝合并为完整文件
- ☁️ **MinIO存储**: 自动上传生成的音频到MinIO对象存储
- 🚀 **异步处理**: 支持后台任务处理，提供任务状态查询
- 📊 **完整API文档**: 自动生成的OpenAPI文档

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境变量

创建 `.env` 文件或设置环境变量：

```bash
# MinIO配置
MINIO_ENDPOINT=http://**************:9000
MINIO_ACCESS_KEY=minio
MINIO_SECRET_KEY=minio@2023
MINIO_BUCKET=tts-audio

# TTS配置
MAX_TEXT_LENGTH=500
COSYVOICE_MODEL_PATH=cosyvoice

# 服务配置
HOST=0.0.0.0
PORT=8000
```

### 3. 启动服务

```bash
python main.py
```

服务将在 `http://localhost:8000` 启动。

## API接口文档

### 1. 文本转语音 `/tts`

**POST** `/tts`

将中文文本转换为语音并上传到MinIO存储。

#### 请求参数

```json
{
    "text": "这是要转换为语音的中文文本内容。支持长文本，系统会自动分段处理。",
    "speaker": "default",
    "speed": 1.0
}
```

| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| text | string | 是 | 要转换的中文文本 (1-10000字符) |
| speaker | string | 否 | 说话人声音选择 (默认: "default") |
| speed | float | 否 | 语速调节 (0.5-2.0，默认: 1.0) |

#### 响应示例

```json
{
    "success": true,
    "task_id": "123e4567-e89b-12d3-a456-426614174000",
    "segments_count": 3,
    "message": "TTS任务已创建，文本分为3段，正在处理中..."
}
```

### 2. 查询任务状态 `/status/{task_id}`

**GET** `/status/{task_id}`

查询TTS任务的处理状态和进度。

#### 响应示例

```json
{
    "task_id": "123e4567-e89b-12d3-a456-426614174000",
    "status": "completed",
    "progress": 100,
    "message": "任务完成",
    "audio_url": "http://**************:9000/tts-audio/tts_audio/20240905_143052_tts_123e4567-e89b-12d3-a456-426614174000.wav"
}
```

#### 任务状态说明

| 状态 | 说明 |
|------|------|
| pending | 任务已创建，等待处理 |
| processing | 正在处理中 |
| completed | 任务完成 |
| failed | 任务失败 |

### 3. 删除任务 `/tasks/{task_id}`

**DELETE** `/tasks/{task_id}`

删除指定任务及其相关资源。

### 4. 健康检查 `/health`

**GET** `/health`

检查服务健康状态。

## 使用示例

### Python示例

```python
import requests
import time

# 1. 提交TTS任务
response = requests.post("http://localhost:8000/tts", json={
    "text": "你好，这是一段测试文本。我们将测试CosyVoice 2.0的语音合成效果。这段文本会被自动分段处理，最终合并为一个完整的音频文件。",
    "speaker": "default",
    "speed": 1.0
})

task_data = response.json()
task_id = task_data["task_id"]
print(f"任务已创建: {task_id}")

# 2. 轮询任务状态
while True:
    status_response = requests.get(f"http://localhost:8000/status/{task_id}")
    status_data = status_response.json()
    
    print(f"任务状态: {status_data['status']} - 进度: {status_data['progress']}% - {status_data['message']}")
    
    if status_data["status"] == "completed":
        print(f"音频文件URL: {status_data['audio_url']}")
        break
    elif status_data["status"] == "failed":
        print("任务失败")
        break
    
    time.sleep(2)
```

### cURL示例

```bash
# 1. 提交TTS任务
curl -X POST "http://localhost:8000/tts" \
     -H "Content-Type: application/json" \
     -d '{
         "text": "这是要转换为语音的测试文本。",
         "speaker": "default",
         "speed": 1.0
     }'

# 2. 查询任务状态
curl "http://localhost:8000/status/{task_id}"

# 3. 删除任务
curl -X DELETE "http://localhost:8000/tasks/{task_id}"
```

## 配置说明

### 文本分段配置

```python
MAX_TEXT_LENGTH = 500  # 单段最大字符数
SEGMENT_DELIMITER = "。！？\n"  # 分段分隔符
```

### MinIO配置

确保MinIO服务可访问，并且有相应的读写权限。

### CosyVoice配置

需要预先安装和配置CosyVoice 2.0模型。如果模型未正确安装，系统会使用模拟模式进行开发测试。

## 项目结构

```
tts-api/
├── main.py              # FastAPI主应用
├── config.py            # 配置文件
├── tts_engine.py        # CosyVoice TTS引擎
├── text_segmenter.py    # 文本分段处理
├── audio_merger.py      # 音频合并功能
├── minio_client.py      # MinIO客户端
├── requirements.txt     # 依赖包列表
└── README.md           # 项目文档
```

## API文档

启动服务后，访问以下地址查看完整的API文档：

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 注意事项

1. **模型依赖**: 需要正确安装CosyVoice 2.0模型
2. **存储空间**: 确保MinIO有足够的存储空间
3. **网络访问**: 确保API服务能够访问MinIO服务
4. **资源清理**: 系统会自动清理临时文件，但建议定期清理过期的任务记录
5. **并发处理**: 当前版本使用内存存储任务状态，生产环境建议使用Redis或数据库

## 错误处理

API会返回详细的错误信息，包括：

- 400: 请求参数错误
- 404: 任务不存在
- 500: 服务器内部错误

所有错误都包含具体的错误描述，便于调试和问题定位。