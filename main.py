from fastapi import FastAPI, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
import logging
import os
import uuid
from datetime import datetime

from text_segmenter import text_segmenter
from tts_engine import tts_engine
from audio_merger import audio_merger
from minio_client import minio_client
from config import config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="TTS API with CosyVoice 2.0",
    description="Text-to-Speech API using CosyVoice 2.0 with automatic text segmentation and MinIO storage",
    version="1.0.0"
)

class TTSRequest(BaseModel):
    text: str = Field(..., description="要转换为语音的中文文本", min_length=1, max_length=10000)
    speaker: Optional[str] = Field("default", description="说话人声音选择")
    speed: Optional[float] = Field(1.0, description="语速调节 (0.5-2.0)", ge=0.5, le=2.0)

class TTSResponse(BaseModel):
    success: bool = Field(..., description="请求是否成功")
    audio_url: Optional[str] = Field(None, description="生成的音频文件URL")
    task_id: str = Field(..., description="任务ID")
    segments_count: int = Field(..., description="文本分段数量")
    total_duration: Optional[float] = Field(None, description="音频总时长（秒）")
    message: str = Field(..., description="响应消息")

class TaskStatusResponse(BaseModel):
    task_id: str
    status: str  # pending, processing, completed, failed
    progress: int  # 0-100
    message: str
    audio_url: Optional[str] = None

# 任务状态存储 (在生产环境中应使用数据库)
task_status: Dict[str, Dict[str, Any]] = {}

@app.get("/")
async def root():
    """API根路径"""
    return {
        "message": "TTS API with CosyVoice 2.0",
        "version": "1.0.0",
        "endpoints": {
            "tts": "/tts",
            "status": "/status/{task_id}",
            "health": "/health"
        }
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    is_mock_model = hasattr(tts_engine.model, '__class__') and tts_engine.model.__class__.__name__ == 'MockTTSModel'
    
    return {
        "status": "healthy" if not is_mock_model else "warning",
        "timestamp": datetime.now().isoformat(),
        "minio_connected": True,  # 可以添加实际的连接检查
        "tts_engine_loaded": tts_engine.model is not None,
        "tts_engine_type": tts_engine.model.__class__.__name__ if tts_engine.model else "None",
        "warning": "Using mock TTS model - install CosyVoice for real speech" if is_mock_model else None
    }

@app.post("/tts", response_model=TTSResponse)
async def text_to_speech(request: TTSRequest, background_tasks: BackgroundTasks):
    """
    文本转语音接口
    
    - **text**: 要转换的中文文本，支持长文本自动分段
    - **speaker**: 说话人选择（可选）
    - **speed**: 语速调节（可选，范围0.5-2.0）
    
    返回生成的音频文件URL和任务信息
    """
    task_id = str(uuid.uuid4())
    
    try:
        # 初始化任务状态
        task_status[task_id] = {
            "status": "pending",
            "progress": 0,
            "message": "任务已创建",
            "created_at": datetime.now().isoformat()
        }
        
        # 文本预处理和分段
        text_segments = text_segmenter.segment_text(request.text)
        segments_count = len(text_segments)
        
        logger.info(f"Task {task_id}: Text segmented into {segments_count} parts")
        
        # 更新任务状态
        task_status[task_id].update({
            "status": "processing",
            "progress": 10,
            "message": f"文本已分段，共{segments_count}段",
            "segments_count": segments_count
        })
        
        # 在后台处理TTS任务
        background_tasks.add_task(
            process_tts_task, 
            task_id, 
            text_segments, 
            request.speaker
        )
        
        return TTSResponse(
            success=True,
            task_id=task_id,
            segments_count=segments_count,
            message=f"TTS任务已创建，文本分为{segments_count}段，正在处理中..."
        )
        
    except Exception as e:
        logger.error(f"Error creating TTS task: {e}")
        if task_id in task_status:
            task_status[task_id].update({
                "status": "failed",
                "progress": 0,
                "message": f"任务创建失败: {str(e)}"
            })
        
        raise HTTPException(
            status_code=500,
            detail=f"TTS任务创建失败: {str(e)}"
        )

@app.get("/status/{task_id}", response_model=TaskStatusResponse)
async def get_task_status(task_id: str):
    """
    获取TTS任务状态
    
    - **task_id**: 任务ID
    
    返回任务的当前状态和进度
    """
    if task_id not in task_status:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    status_info = task_status[task_id]
    
    return TaskStatusResponse(
        task_id=task_id,
        status=status_info["status"],
        progress=status_info["progress"],
        message=status_info["message"],
        audio_url=status_info.get("audio_url")
    )

async def process_tts_task(task_id: str, text_segments: list, speaker: str):
    """
    后台处理TTS任务
    """
    try:
        # 生成各段语音
        task_status[task_id].update({
            "progress": 20,
            "message": "正在生成语音..."
        })
        
        audio_files = tts_engine.generate_speech_batch(text_segments, speaker)
        
        # 更新进度
        task_status[task_id].update({
            "progress": 60,
            "message": "语音生成完成，正在合并..."
        })
        
        # 合并音频文件
        merged_audio_path = audio_merger.merge_audio_files(audio_files)
        
        # 更新进度
        task_status[task_id].update({
            "progress": 80,
            "message": "音频合并完成，正在上传..."
        })
        
        # 上传到MinIO
        audio_url = minio_client.upload_file(merged_audio_path, f"tts_{task_id}.wav")
        
        # 获取音频信息
        audio_info = audio_merger.get_audio_info(merged_audio_path)
        
        # 清理本地文件
        if os.path.exists(merged_audio_path):
            os.remove(merged_audio_path)
        
        # 任务完成
        task_status[task_id].update({
            "status": "completed",
            "progress": 100,
            "message": "任务完成",
            "audio_url": audio_url,
            "audio_info": audio_info,
            "completed_at": datetime.now().isoformat()
        })
        
        logger.info(f"Task {task_id} completed successfully: {audio_url}")
        
    except Exception as e:
        logger.error(f"Error processing TTS task {task_id}: {e}")
        task_status[task_id].update({
            "status": "failed",
            "progress": 0,
            "message": f"任务处理失败: {str(e)}",
            "failed_at": datetime.now().isoformat()
        })

@app.delete("/tasks/{task_id}")
async def delete_task(task_id: str):
    """删除任务记录"""
    if task_id not in task_status:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    # 删除MinIO中的文件（如果存在）
    status_info = task_status[task_id]
    if "audio_url" in status_info:
        try:
            # 从URL提取对象名称
            audio_url = status_info["audio_url"]
            object_name = audio_url.split("/")[-1]
            minio_client.delete_file(f"tts_audio/{object_name}")
        except Exception as e:
            logger.warning(f"Error deleting audio file for task {task_id}: {e}")
    
    # 删除任务状态
    del task_status[task_id]
    
    return {"message": "任务已删除"}

if __name__ == "__main__":
    import uvicorn
    
    # 确保临时目录存在
    os.makedirs(config.TEMP_DIR, exist_ok=True)
    
    uvicorn.run(
        "main:app",
        host=config.HOST,
        port=config.PORT,
        reload=False
    )