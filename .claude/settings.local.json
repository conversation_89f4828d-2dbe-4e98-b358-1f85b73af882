{"permissions": {"allow": ["<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(uv pip search:*)", "Bash(uv pip install:*)", "Bash(pip install:*)", "<PERSON><PERSON>(pip show:*)", "Bash(.venv/bin/pip install:*)", "Read(//root/.cache/**)", "<PERSON><PERSON>(uv pip uninstall:*)", "Read(//tmp/**)", "WebSearch", "Bash(COSYVOICE_MODEL_PATH=\"./pretrained_models/CosyVoice2-0.5B\" PORT=12524 python main.py)", "Bash(git submodule update:*)", "Bash(COSYVOICE_MODEL_PATH=\"./pretrained_models/CosyVoice2-0.5B\" python main.py)", "Bash(COSYVOICE_MODEL_PATH=\"./pretrained_models/CosyVoice2-0.5B\" PORT=12525 python main.py)", "Bash(COSYVOICE_MODEL_PATH=\"./pretrained_models/CosyVoice2-0.5B\" PORT=12526 python main.py)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(curl:*)", "WebFetch(domain:github.com)"], "deny": [], "ask": []}}