version: '3.8'

services:
  tts-api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - MINIO_ENDPOINT=http://minio:9000
      - MINIO_ACCESS_KEY=minio
      - MINIO_SECRET_KEY=minio@2023
      - MINIO_BUCKET=tts-audio
      - MAX_TEXT_LENGTH=500
      - HOST=0.0.0.0
      - PORT=8000
    depends_on:
      - minio
    volumes:
      - /tmp/tts:/tmp/tts

  minio:
    image: minio/minio:latest
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=minio
      - MINIO_ROOT_PASSWORD=minio@2023
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"

volumes:
  minio_data: