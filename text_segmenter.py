import re
from typing import List
from config import config

class TextSegmenter:
    def __init__(self):
        self.max_length = config.MAX_TEXT_LENGTH
        self.delimiters = config.SEGMENT_DELIMITER
    
    def segment_text(self, text: str) -> List[str]:
        """
        将长文本分段处理
        
        Args:
            text: 输入文本
            
        Returns:
            分段后的文本列表
        """
        if len(text) <= self.max_length:
            return [text.strip()]
        
        segments = []
        current_segment = ""
        
        # 按标点符号分割
        sentences = re.split(f'([{re.escape(self.delimiters)}])', text)
        
        for i in range(0, len(sentences), 2):
            sentence = sentences[i] if i < len(sentences) else ""
            delimiter = sentences[i + 1] if i + 1 < len(sentences) else ""
            
            full_sentence = sentence + delimiter
            
            # 如果当前段落加上新句子超过最大长度
            if len(current_segment + full_sentence) > self.max_length:
                if current_segment:
                    segments.append(current_segment.strip())
                    current_segment = full_sentence
                else:
                    # 单句话就超过最大长度，强制分割
                    segments.extend(self._force_split(full_sentence))
            else:
                current_segment += full_sentence
        
        # 添加最后一段
        if current_segment:
            segments.append(current_segment.strip())
        
        # 过滤空段落
        return [seg for seg in segments if seg.strip()]
    
    def _force_split(self, text: str) -> List[str]:
        """强制按字符数分割超长句子"""
        segments = []
        for i in range(0, len(text), self.max_length):
            segment = text[i:i + self.max_length]
            if segment.strip():
                segments.append(segment.strip())
        return segments

text_segmenter = TextSegmenter()