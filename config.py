import os
from typing import Optional

class Config:
    # MinIO Configuration
    MINIO_ENDPOINT = os.getenv("MINIO_ENDPOINT", "http://**************:9000")
    MINIO_ACCESS_KEY = os.getenv("MINIO_ACCESS_KEY", "minio")
    MINIO_SECRET_KEY = os.getenv("MINIO_SECRET_KEY", "minio@2023")
    MINIO_BUCKET = os.getenv("MINIO_BUCKET", "tts-audio")
    
    # TTS Configuration
    MAX_TEXT_LENGTH = int(os.getenv("MAX_TEXT_LENGTH", "500"))
    SEGMENT_DELIMITER = os.getenv("SEGMENT_DELIMITER", "。！？\n")
    TEMP_DIR = os.getenv("TEMP_DIR", "/tmp/tts")
    
    # API Configuration
    HOST = os.getenv("HOST", "0.0.0.0")
    PORT = int(os.getenv("PORT", "12523"))
    
    # CosyVoice Configuration
    COSYVOICE_MODEL_PATH = os.getenv("COSYVOICE_MODEL_PATH", "cosyvoice")

config = Config()