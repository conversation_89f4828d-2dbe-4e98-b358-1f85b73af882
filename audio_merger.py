import os
import tempfile
from typing import List
from pydub import AudioSegment
import logging

logger = logging.getLogger(__name__)

class AudioMerger:
    def __init__(self):
        pass
    
    def merge_audio_files(self, audio_files: List[str], output_path: str = None) -> str:
        """
        合并多个音频文件
        
        Args:
            audio_files: 音频文件路径列表
            output_path: 输出文件路径，如果为None则创建临时文件
            
        Returns:
            合并后的音频文件路径
        """
        if not audio_files:
            raise ValueError("No audio files provided")
        
        if len(audio_files) == 1:
            # 如果只有一个文件，直接返回
            return audio_files[0]
        
        try:
            # 创建输出文件路径
            if output_path is None:
                with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                    output_path = temp_file.name
            
            # 加载第一个音频文件
            merged_audio = AudioSegment.from_wav(audio_files[0])
            logger.info(f"Loaded first audio file: {audio_files[0]}")
            
            # 逐个合并其他音频文件
            for i, audio_file in enumerate(audio_files[1:], 1):
                try:
                    audio_segment = AudioSegment.from_wav(audio_file)
                    merged_audio += audio_segment
                    logger.info(f"Merged audio file {i+1}/{len(audio_files)}: {audio_file}")
                except Exception as e:
                    logger.error(f"Error merging audio file {audio_file}: {e}")
                    raise
            
            # 导出合并后的音频
            merged_audio.export(output_path, format="wav")
            logger.info(f"Audio files merged successfully: {output_path}")
            
            # 清理临时文件
            self._cleanup_temp_files(audio_files)
            
            return output_path
            
        except Exception as e:
            logger.error(f"Error merging audio files: {e}")
            raise
    
    def _cleanup_temp_files(self, temp_files: List[str]):
        """清理临时文件"""
        for temp_file in temp_files:
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                    logger.debug(f"Cleaned up temp file: {temp_file}")
            except Exception as e:
                logger.warning(f"Error cleaning up temp file {temp_file}: {e}")
    
    def get_audio_info(self, audio_path: str) -> dict:
        """获取音频文件信息"""
        try:
            audio = AudioSegment.from_wav(audio_path)
            return {
                "duration": len(audio) / 1000.0,  # 秒
                "channels": audio.channels,
                "frame_rate": audio.frame_rate,
                "sample_width": audio.sample_width,
                "file_size": os.path.getsize(audio_path)
            }
        except Exception as e:
            logger.error(f"Error getting audio info: {e}")
            return {}

# 全局音频合并实例
audio_merger = AudioMerger()