from minio import Minio
from minio.error import S3Error
from config import config
import logging
import os
from datetime import datetime

logger = logging.getLogger(__name__)

class MinIOClient:
    def __init__(self):
        self.client = Minio(
            endpoint=config.MINIO_ENDPOINT.replace("http://", "").replace("https://", ""),
            access_key=config.MINIO_ACCESS_KEY,
            secret_key=config.MINIO_SECRET_KEY,
            secure=config.MINIO_ENDPOINT.startswith("https://")
        )
        self._ensure_bucket_exists()
    
    def _ensure_bucket_exists(self):
        """确保存储桶存在"""
        try:
            if not self.client.bucket_exists(config.MINIO_BUCKET):
                self.client.make_bucket(config.MINIO_BUCKET)
                logger.info(f"Created bucket: {config.MINIO_BUCKET}")
        except S3Error as e:
            logger.error(f"Error creating bucket: {e}")
            raise
    
    def upload_file(self, file_path: str, object_name: str = None) -> str:
        """
        上传文件到MinIO
        
        Args:
            file_path: 本地文件路径
            object_name: 对象名称，如果为None则使用文件名
            
        Returns:
            上传后的文件URL
        """
        if object_name is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = os.path.basename(file_path)
            object_name = f"tts_audio/{timestamp}_{filename}"
        
        try:
            self.client.fput_object(
                bucket_name=config.MINIO_BUCKET,
                object_name=object_name,
                file_path=file_path,
                content_type="audio/wav"
            )
            
            # 生成访问URL
            url = f"{config.MINIO_ENDPOINT}/{config.MINIO_BUCKET}/{object_name}"
            logger.info(f"File uploaded successfully: {url}")
            return url
            
        except S3Error as e:
            logger.error(f"Error uploading file: {e}")
            raise
    
    def delete_file(self, object_name: str):
        """删除文件"""
        try:
            self.client.remove_object(config.MINIO_BUCKET, object_name)
            logger.info(f"File deleted: {object_name}")
        except S3Error as e:
            logger.error(f"Error deleting file: {e}")

minio_client = MinIOClient()