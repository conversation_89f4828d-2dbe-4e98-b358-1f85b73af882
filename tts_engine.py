import os
import sys
import torch
import torchaudio
from typing import List, Optional
import tempfile
from config import config
import logging

# 添加Matcha-TTS路径到系统路径
sys.path.append('/root/.cache/uv/git-v0/checkouts/d97756bdab24d1f2/86e7c2d/third_party/Matcha-TTS')

logger = logging.getLogger(__name__)

class CosyVoiceTTS:
    def __init__(self):
        self.model = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self._load_model()
    
    def _load_model(self):
        """加载CosyVoice模型"""
        try:
            # 尝试加载CosyVoice 2.0
            from cosyvoice.cli.cosyvoice import CosyVoice2
            
            # 检查模型路径是否存在
            if not os.path.exists(config.COSYVOICE_MODEL_PATH):
                logger.error(f"CosyVoice model path not found: {config.COSYVOICE_MODEL_PATH}")
                logger.error("Please download CosyVoice model first!")
                self.model = self._create_mock_model()
                return
                
            # 使用官方推荐的参数初始化CosyVoice2
            self.model = CosyVoice2(
                config.COSYVOICE_MODEL_PATH,
                load_jit=False,    # 不使用JIT以保证兼容性
                load_trt=False,    # 不使用TensorRT
                fp16=False         # 不使用FP16以保证质量
            )
            logger.info(f"CosyVoice model loaded successfully from {config.COSYVOICE_MODEL_PATH}")
            logger.info(f"Running on device: {self.device}")
            
        except ImportError as e:
            logger.error("=" * 60)
            logger.error("CosyVoice library not found!")
            logger.error("Please install it with: pip install cosyvoice")
            logger.error("Or install from source: pip install git+https://github.com/FunAudioLLM/CosyVoice.git")
            logger.error("=" * 60)
            logger.error(f"Error details: {e}")
            self.model = self._create_mock_model()
            
        except Exception as e:
            logger.error(f"Error loading CosyVoice model: {e}")
            logger.error("Check model path and model files are correct")
            self.model = self._create_mock_model()
    
    def _create_mock_model(self):
        """创建模拟模型用于开发测试"""
        logger.warning("=" * 60)
        logger.warning("WARNING: Using mock TTS model!")
        logger.warning("Generated audio will be a simple tone, not real speech.")
        logger.warning("Please install CosyVoice to get real TTS functionality.")
        logger.warning("=" * 60)
        return MockTTSModel()
    
    def generate_speech(self, text: str, speaker: str = "default") -> str:
        """
        生成语音
        
        Args:
            text: 要转换的文本
            speaker: 说话人（可选）
            
        Returns:
            生成的音频文件路径
        """
        if not self.model:
            raise RuntimeError("TTS model not loaded")
        
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                temp_path = temp_file.name
            
            # 使用CosyVoice2生成语音
            # 使用真实的中文参考音频和对应的提示文本
            prompt_text = "希望你以后能够做的比我还好呦。"
            
            # 加载真实的参考音频文件
            try:
                from cosyvoice.utils.file_utils import load_wav
                reference_audio_path = os.path.join(os.path.dirname(__file__), "zero_shot_prompt.wav")
                if os.path.exists(reference_audio_path):
                    prompt_speech_16k = load_wav(reference_audio_path, 16000)
                    logger.info(f"Using reference audio: {reference_audio_path}")
                else:
                    # 如果参考音频文件不存在，生成一个短的中文示例音频
                    logger.warning("Reference audio not found, using minimal prompt")
                    prompt_speech_16k = torch.randn(1, 8000) * 0.1  # 使用小噪声代替静音
            except Exception as e:
                logger.warning(f"Failed to load reference audio: {e}, using minimal prompt")
                prompt_speech_16k = torch.randn(1, 8000) * 0.1  # 使用小噪声代替静音
            
            # 生成语音 - 使用zero_shot方法，添加text_frontend=False参数
            # 根据官方文档：如果要重现官网结果，请在推理时添加text_frontend=False
            logger.info(f"Generating speech for text: {text[:50]}...")
            for model_output in self.model.inference_zero_shot(
                text, 
                prompt_text, 
                prompt_speech_16k, 
                stream=False,
                text_frontend=False  # 关键参数：禁用文本前端处理
            ):
                audio_data = model_output['tts_speech']
                break  # 只取第一个输出
            
            # 保存音频文件
            if isinstance(audio_data, torch.Tensor):
                # CosyVoice2使用24000Hz采样率
                sample_rate = getattr(self.model, 'sample_rate', 24000)
                
                # 确保音频数据格式正确
                if audio_data.dim() == 1:
                    # 1D音频数据，添加batch维度
                    audio_data = audio_data.unsqueeze(0)
                elif audio_data.dim() == 3:
                    # 3D数据，可能需要压缩
                    audio_data = audio_data.squeeze(0)
                
                # 确保是单声道音频
                if audio_data.dim() == 2 and audio_data.shape[0] > 1:
                    audio_data = audio_data.mean(dim=0, keepdim=True)
                
                logger.info(f"Audio tensor shape: {audio_data.shape}, sample_rate: {sample_rate}")
                torchaudio.save(temp_path, audio_data.cpu(), sample_rate)
            else:
                # 如果是numpy数组或其他格式，需要相应处理
                import numpy as np
                if isinstance(audio_data, np.ndarray):
                    audio_tensor = torch.from_numpy(audio_data).float()
                    torchaudio.save(temp_path, audio_tensor.unsqueeze(0), 22050)
            
            logger.info(f"Generated audio file: {temp_path}")
            return temp_path
            
        except Exception as e:
            logger.error(f"Error generating speech: {e}")
            raise
    
    def generate_speech_batch(self, texts: List[str], speaker: str = "default") -> List[str]:
        """
        批量生成语音
        
        Args:
            texts: 文本列表
            speaker: 说话人
            
        Returns:
            生成的音频文件路径列表
        """
        audio_files = []
        for i, text in enumerate(texts):
            try:
                audio_file = self.generate_speech(text, speaker)
                audio_files.append(audio_file)
                logger.info(f"Generated segment {i+1}/{len(texts)}")
            except Exception as e:
                logger.error(f"Error generating segment {i+1}: {e}")
                raise
        
        return audio_files


class MockTTSModel:
    """模拟TTS模型用于开发测试"""
    
    def generate(self, text: str, speaker: str = "default"):
        """生成模拟音频数据"""
        import numpy as np
        # 生成一段简单的正弦波作为模拟音频
        duration = len(text) * 0.1  # 根据文本长度估算时长
        sample_rate = 22050
        t = np.linspace(0, duration, int(sample_rate * duration))
        frequency = 440  # A4音符
        audio_data = np.sin(2 * np.pi * frequency * t) * 0.3
        return audio_data.astype(np.float32)


# 全局TTS实例
tts_engine = CosyVoiceTTS()